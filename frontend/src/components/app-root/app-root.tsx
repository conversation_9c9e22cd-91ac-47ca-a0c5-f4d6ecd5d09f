import { Component, FunctionalComponent, Host, Listen, State, h } from '@stencil/core';
import { Router } from '../../';
import { Route, match } from 'stencil-router-v2';
import { Store } from '../../global/script/store';
import {
  setAccountDataInStore,
  generateEmailVerificationPayload,
  validateEmailVerificationPayload,
  emailVerificationApi,
} from './helpers';
import { MailApi } from '../../global/script/helpers';
import { MailPayloadInterface } from '../../global/script/interfaces';
import { GenerateMailPayload, ValidateMailPayload } from '../../global/script/helpers';
import {
  AccountDetailsBySessionApi,
  CheckSessionStatusApi,
  GetCsrfTokenApi,
  LogoutApi,
} from '../../global/script/helpers';
import { emailVerificationPayloadInterface } from './interfaces';

@Component({
  tag: 'app-root',
  styleUrl: 'app-root.css',
  shadow: true,
})
export class AppRoot {
  @State() isMailingEmailVerificationCode: boolean = false;
  @State() isSessionChecked: boolean = false;
  @State() isVerifyingEmail: boolean = false;

  private emailVerificationCode: string = '';

  @Listen('authSuccessfulEvent') handleAuthSuccessfulEvent() {
    this.initSession();
  }

  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    if (e.detail.action === 'mailEmailVerificationCode') {
      this.mailEmailVerificationCode();
    } else if (e.detail.action === 'logout') {
      this.logout();
    } else if (e.detail.action === 'proceedToLogin') {
      Router.push('/login');
    } else if (e.detail.action === 'routeTo') {
      Router.push(e.detail.value);
    }
  }

  @Listen('inputEvent') handleInputEvent(e) {
    if (e.detail.name === 'emailVerificationCode') {
      this.emailVerificationCode = e.detail.value;
      if (this.emailVerificationCode.length === 4) {
        this.verifyEmail();
      }
    }
  }

  @Listen('logoutEvent') handleLogout() {
    this.logout();
  }

  @Listen('routeToEvent') handleRouteToEvent(e) {
    Router.push(e.detail.route);
  }

  @Listen('authSuccess') handleAuthSuccessEvent(e) {
    setAccountDataInStore(e.detail.payload);
  }

  componentDidLoad() {
    this.initSession();
  }

  async initSession() {
    this.isSessionChecked = false;

    try {
      // First, fetch CSRF token to ensure it's available for subsequent requests
      const csrfResult = await GetCsrfTokenApi();

      if (!csrfResult.success) {
        console.error('Failed to fetch CSRF token:', csrfResult.message);
        // Continue anyway, some endpoints might not require CSRF
      }

      // Then check session status with CSRF token available
      const sessionResult = await CheckSessionStatusApi();

      if (sessionResult.success && sessionResult.payload && sessionResult.payload.isSessionActive) {
        Store.isSessionActive = true;
        // Fetch account details if we have a valid session and CSRF token
        if (csrfResult.success || Store.csrfToken) {
          this.fetchAccountDetails();
        }
      } else {
        Store.isSessionActive = false;
        Store.accountName = '';
        Store.accountEmail = '';
      }
    } catch (error) {
      console.error('Error during initialization:', error);
      Store.isSessionActive = false;
      Store.csrfToken = ''; // Clear potentially invalid token
    } finally {
      this.isSessionChecked = true;
    }
  }

  async fetchAccountDetails() {
    try {
      const { success, payload } = await AccountDetailsBySessionApi();
      if (success && payload) {
        setAccountDataInStore(payload);
      }
    } catch (error) {
      console.error('Failed to fetch account details:', error);
    }
  }

  async logout() {
    let { success, message, payload } = await LogoutApi();
    if (!success) {
      return alert(message);
    }

    // Clear all session-related data
    Store.isSessionActive = payload?.isSessionActive || false;
    Store.accountName = '';
    Store.accountEmail = '';
    Store.isEmailVerified = true;

    // CSRF token is already cleared by LogoutApi, but fetch a new anonymous one
    try {
      await GetCsrfTokenApi();
    } catch (error) {
      console.warn('Failed to fetch new CSRF token after logout:', error);
    }

    Router.push('/');
  }

  async mailEmailVerificationCode() {
    let mailEmailVerificationCodePayload: MailPayloadInterface = GenerateMailPayload(
      Store.accountEmail,
      'emailVerificationCode',
    );
    let { isValid, validationMessage } = ValidateMailPayload(mailEmailVerificationCodePayload);
    if (!isValid) {
      return alert(validationMessage);
    }
    this.isMailingEmailVerificationCode = true;
    let { message } = await MailApi(mailEmailVerificationCodePayload);
    this.isMailingEmailVerificationCode = false;
    alert(message);
  }

  async verifyEmail() {
    let emailVerificationPayload: emailVerificationPayloadInterface =
      generateEmailVerificationPayload(Store.accountEmail, this.emailVerificationCode);
    let { isValid, validationMessage } = validateEmailVerificationPayload(emailVerificationPayload);
    if (!isValid) {
      return alert(validationMessage);
    }
    this.isVerifyingEmail = true;
    let { success, message } = await emailVerificationApi(emailVerificationPayload);
    alert(message);
    if (!success) {
      return;
    }
    Store.isEmailVerified = true;
  }

  EmailVerificationBanner: FunctionalComponent = () => (
    <c-banner position="bottom" theme="danger">
      <div class="hide-on-mobile">
        <l-row justifyContent="space-between">
          <l-row>
            <e-text>
              We have sent an email verification code to{' '}
              <u>
                <strong>{Store.accountEmail}</strong>
              </u>
            </e-text>
            <l-spacer variant="horizontal" value={0.25}></l-spacer>
            <e-input
              type="text"
              name="emailVerificationCode"
              placeholder="Enter verification code"
            ></e-input>
          </l-row>
          {this.isMailingEmailVerificationCode ? (
            <e-spinner theme="blue"></e-spinner>
          ) : (
            <e-button variant="link" action="mailEmailVerificationCode">
              Re-send code
            </e-button>
          )}
        </l-row>
      </div>
      <div class="show-on-mobile">
        <e-text>
          We have sent an email verification code to{' '}
          <strong>
            <u>{Store.accountEmail}</u>
          </strong>
        </e-text>
        <l-spacer value={1}></l-spacer>
        <l-row>
          <e-input
            type="text"
            name="emailVerificationCode"
            placeholder="Enter verification code"
          ></e-input>
          {this.isMailingEmailVerificationCode ? (
            <e-spinner theme="blue"></e-spinner>
          ) : (
            <e-button variant="link" action="mailEmailVerificationCode">
              Re-send code
            </e-button>
          )}
        </l-row>
      </div>
    </c-banner>
  );

  private getRedirectRoutes() {
    if (Store.isSessionActive) {
      return [
        <Route path={/^\/$/} to="/home" />,
        <Route path={/^\/login$/} to="/home" />,
        <Route path={/^\/signup$/} to="/home" />,
        <Route path={/^\/auth\/callback\/[^\/]+$/} to="/home" />,
        <Route path={/^\/password-reset$/} to="/home" />,
      ];
    } else {
      return [
        <Route path={/^\/$/} to="/login" />,
        <Route path={/^\/home$/} to="/login" />,
        <Route path={/^\/billing/} to="/login" />,
        <Route path={/^\/support/} to="/login" />,
        <Route path={/^\/account/} to="/login" />,
        <Route path={/^\/account\/delete/} to="/login" />,
        <Route path={/^\/checkout$/} to="/login" />,
        <Route path={/^\/payment\/[^\/]+\/success$/} to="/login" />,
        <Route path={/^\/payment\/[^\/]+\/failed$/} to="/login" />,
        <Route path={/^\/surveys\/create$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/delete$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/edit$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/embed$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/share$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/results$/} to="/login" />,
        <Route path={/^\/s$/} to="/login" />,
      ];
    }
  }

  render() {
    if (!this.isSessionChecked) {
      return (
        <Host>
          <div id="init-loader">
            <e-spinner theme="dark"></e-spinner>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        {Store.isSessionActive && <p-topbar></p-topbar>}

        <Router.Switch>
          {this.getRedirectRoutes()}
          {/* Auth Routes */}
          <Route path="/login" render={() => <v-login />}></Route>
          <Route path="/signup" render={() => <v-signup />}></Route>
          <Route
            path={match('/auth/callback/:provider')}
            render={({ provider }) => <v-post-oauth provider={provider} />}
          />
          <Route path="/password-reset" render={() => <v-password-reset />}></Route>
          <Route path="/home" render={() => <v-home />}></Route>
          <Route path="/billing" render={() => <v-billing />}></Route>
          <Route path="/support" render={() => <v-support />}></Route>
          <Route path="/account" render={() => <v-account />}></Route>
          <Route path="/account/delete" render={() => <v-delete-account />}></Route>
          <Route
            path="/checkout/:orderId"
            render={({ orderId }) => <v-checkout orderId={orderId} />}
          />
          <Route
            path={match('/payment/:sessionId/success')}
            render={({ sessionId }) => <v-payment-success sessionId={sessionId} />}
          />
          <Route
            path={match('/payment/:sessionId/failed')}
            render={({ sessionId }) => <v-payment-failed sessionId={sessionId} />}
          />
          {/* Survey Routes */}
          <Route path="/surveys/create" render={() => <v-create-survey />}></Route>
          <Route
            path={match('/surveys/:surveyId/delete')}
            render={({ surveyId }) => <v-delete-survey surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/edit')}
            render={({ surveyId }) => <v-edit-survey surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/embed')}
            render={({ surveyId }) => <v-embed-survey surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/share')}
            render={({ surveyId }) => <v-share-survey surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/results')}
            render={({ surveyId }) => <v-survey-results surveyId={surveyId} />}
          />
          <Route
            path={match('/s/:shareKey')}
            render={({ shareKey }) => <v-shared-survey shareKey={shareKey} />}
          />
          {/* Development/Testing Routes */}
          <Route path="/test/csrf" render={() => <v-csrf-test />}></Route>
          <Route path={/.*/} render={() => <v-catch-all></v-catch-all>}></Route>
        </Router.Switch>

        {Store.isSessionActive && !Store.isEmailVerified && (
          <this.EmailVerificationBanner></this.EmailVerificationBanner>
        )}
      </Host>
    );
  }
}
