.logout-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.test-controls {
  margin-bottom: 20px;
}

.test-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.test-button:hover {
  background-color: #0056b3;
}

.test-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.test-results {
  margin-bottom: 20px;
}

.results-container {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  margin-bottom: 5px;
  padding: 5px;
  font-family: monospace;
  font-size: 14px;
  border-bottom: 1px solid #e9ecef;
}

.result-item:last-child {
  border-bottom: none;
}

.current-state {
  background-color: #e9ecef;
  padding: 15px;
  border-radius: 4px;
}

.current-state h3 {
  margin-top: 0;
}

.current-state p {
  margin: 5px 0;
  font-family: monospace;
}
