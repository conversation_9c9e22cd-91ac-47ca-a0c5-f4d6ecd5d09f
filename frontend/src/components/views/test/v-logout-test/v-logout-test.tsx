import { Component, State, h } from '@stencil/core';
import { LogoutApi } from '../../../../global/script/helpers';
import { Store } from '../../../../global/script/store';

@Component({
  tag: 'v-logout-test',
  styleUrl: 'v-logout-test.css',
  shadow: true,
})
export class VLogoutTest {
  @State() testResults: string[] = [];
  @State() isLoading: boolean = false;

  addResult(message: string) {
    this.testResults = [...this.testResults, `${new Date().toLocaleTimeString()}: ${message}`];
  }

  async testLogout() {
    this.isLoading = true;
    this.testResults = [];

    try {
      // Test 1: Check initial state
      this.addResult(`Initial session state: ${Store.isSessionActive ? 'ACTIVE' : 'INACTIVE'}`);
      this.addResult(`Initial CSRF token: ${Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none'}`);

      // Test 2: Attempt logout
      this.addResult('--- Attempting Logout ---');
      const logoutResult = await LogoutApi();
      this.addResult(`Logout result: ${logoutResult.success ? 'SUCCESS' : 'FAILED'} - ${logoutResult.message}`);

      // Test 3: Check final state
      this.addResult(`Final session state: ${Store.isSessionActive ? 'ACTIVE' : 'INACTIVE'}`);
      this.addResult(`Final CSRF token: ${Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none'}`);

      // Test 4: Try to fetch new CSRF token
      this.addResult('--- Testing CSRF Token Fetch After Logout ---');
      try {
        const { GetCsrfTokenApi } = await import('../../../../global/script/helpers');
        const csrfResult = await GetCsrfTokenApi();
        this.addResult(`CSRF fetch result: ${csrfResult.success ? 'SUCCESS' : 'FAILED'} - ${csrfResult.message}`);
        this.addResult(`New CSRF token: ${Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none'}`);
      } catch (error) {
        this.addResult(`CSRF fetch error: ${error instanceof Error ? error.message : String(error)}`);
      }

    } catch (error) {
      this.addResult(`Test error: ${error instanceof Error ? error.message : String(error)}`);
    }

    this.isLoading = false;
  }

  render() {
    return (
      <div class="logout-test">
        <h2>Logout CSRF Test</h2>
        
        <div class="test-controls">
          <button 
            onClick={() => this.testLogout()} 
            disabled={this.isLoading}
            class="test-button"
          >
            {this.isLoading ? 'Testing...' : 'Test Logout Flow'}
          </button>
        </div>

        <div class="test-results">
          <h3>Test Results:</h3>
          <div class="results-container">
            {this.testResults.map((result, index) => (
              <div key={index} class="result-item">
                {result}
              </div>
            ))}
          </div>
        </div>

        <div class="current-state">
          <h3>Current State:</h3>
          <p>Session Active: {Store.isSessionActive ? 'YES' : 'NO'}</p>
          <p>CSRF Token: {Store.csrfToken ? Store.csrfToken.substring(0, 20) + '...' : 'NONE'}</p>
        </div>
      </div>
    );
  }
}
