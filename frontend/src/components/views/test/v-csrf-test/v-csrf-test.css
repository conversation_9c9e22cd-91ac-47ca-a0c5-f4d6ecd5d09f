.csrf-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.controls {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.controls button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.controls button:nth-child(2) {
  background-color: #ffc107;
  color: #212529;
}

.controls button:nth-child(3) {
  background-color: #dc3545;
}

.controls button:last-child {
  background-color: #6c757d;
}

.controls button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.controls button:hover:not(:disabled) {
  filter: brightness(0.9);
}

.controls button:nth-child(2):hover:not(:disabled) {
  background-color: #e0a800;
}

.controls button:nth-child(3):hover:not(:disabled) {
  background-color: #c82333;
}

.controls button:last-child:hover:not(:disabled) {
  background-color: #5a6268;
}

.current-state {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.current-state h3 {
  margin-top: 0;
  color: #495057;
}

.current-state p {
  margin: 5px 0;
  font-family: monospace;
}

.test-results {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

.test-results h3 {
  margin-top: 0;
  color: #495057;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
}

.result-item {
  padding: 5px 0;
  border-bottom: 1px solid #e9ecef;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.result-item:last-child {
  border-bottom: none;
}
