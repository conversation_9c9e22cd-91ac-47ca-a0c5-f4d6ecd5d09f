import { Component, h, State } from '@stencil/core';
import { Store } from '../../../../global/script/store';
import { GetCsrfTokenApi } from '../../../../global/script/helpers/api/GetCsrfTokenApi';
import { ApiWrapper } from '../../../../global/script/helpers/api/ApiWrapper';
import { CsrfValidator } from '../../../../global/script/helpers/security/CsrfValidator';
import { RateLimitHandler } from '../../../../global/script/helpers/api/RateLimitHandler';

@Component({
  tag: 'v-csrf-test',
  styleUrl: 'v-csrf-test.css',
  shadow: true,
})
export class VCsrfTest {
  @State() testResults: string[] = [];
  @State() isLoading: boolean = false;
  @State() showRateLimitNotification: boolean = false;
  @State() rateLimitedEndpoint: string = '';

  async runCsrfTests() {
    this.isLoading = true;
    this.testResults = [];

    try {
      // Test 1: Check initial token state
      this.addResult(
        `Initial CSRF token: ${
          Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none'
        }`,
      );
      this.addResult(`Token valid: ${CsrfValidator.isTokenValid()}`);
      this.addResult(`Token format valid: ${CsrfValidator.isTokenFormatValid()}`);

      // Test 2: Fetch CSRF token
      this.addResult('--- Fetching CSRF Token ---');
      const csrfResult = await GetCsrfTokenApi();
      this.addResult(
        `CSRF fetch result: ${csrfResult.success ? 'SUCCESS' : 'FAILED'} - ${csrfResult.message}`,
      );
      this.addResult(
        `New token: ${Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none'}`,
      );

      // Test 3: Test API wrapper with GET request
      this.addResult('--- Testing API Wrapper (GET) ---');
      try {
        const getResult = await ApiWrapper('/csrf-token', {
          method: 'GET',
          includeCsrf: false,
        });
        this.addResult(
          `GET request result: ${getResult.success ? 'SUCCESS' : 'FAILED'} - ${getResult.message}`,
        );
      } catch (error) {
        this.addResult(
          `GET request error: ${error instanceof Error ? error.message : String(error)}`,
        );
      }

      // Test 4: Test API wrapper with POST request (if backend is available)
      this.addResult('--- Testing API Wrapper (POST) ---');
      try {
        const postResult = await ApiWrapper('/csrf-test', {
          method: 'POST',
          body: { test: 'data' },
        });
        this.addResult(
          `POST request result: ${postResult.success ? 'SUCCESS' : 'FAILED'} - ${
            postResult.message
          }`,
        );
      } catch (error) {
        this.addResult(
          `POST request error: ${error instanceof Error ? error.message : String(error)}`,
        );
      }

      // Test 5: Rate limiting tests
      this.addResult('--- Rate Limiting Tests ---');
      try {
        // Test multiple rapid requests to trigger rate limiting
        const rapidRequests = [];
        for (let i = 0; i < 3; i++) {
          rapidRequests.push(
            ApiWrapper('/csrf-token', {
              method: 'GET',
              includeCsrf: false,
            }),
          );
        }

        const rapidResults = await Promise.all(rapidRequests);
        rapidResults.forEach((result, index) => {
          this.addResult(
            `Rapid request ${index + 1}: ${result.success ? 'SUCCESS' : 'FAILED'} - ${
              result.message
            }`,
          );
          if (result.rateLimitInfo) {
            this.addResult(`  Rate limit info: ${JSON.stringify(result.rateLimitInfo)}`);
            if (result.rateLimitInfo.isRateLimited) {
              this.showRateLimitNotification = true;
              this.rateLimitedEndpoint = '/csrf-token';
            }
          }
        });
      } catch (error) {
        this.addResult(
          `Rate limiting test error: ${error instanceof Error ? error.message : String(error)}`,
        );
      }

      // Test 6: Rate limit handler
      this.addResult('--- Rate Limit Handler Tests ---');
      const rateLimitedEndpoints = RateLimitHandler.getRateLimitedEndpoints();
      this.addResult(`Currently rate limited endpoints: ${JSON.stringify(rateLimitedEndpoints)}`);

      if (rateLimitedEndpoints.length > 0) {
        const endpoint = rateLimitedEndpoints[0];
        this.addResult(
          `Time until reset for ${endpoint}: ${RateLimitHandler.getTimeUntilReset(endpoint)}s`,
        );
        this.addResult(`Rate limit message: ${RateLimitHandler.getRateLimitMessage(endpoint)}`);
      }

      // Test 7: Token validation
      this.addResult('--- Token Validation ---');
      this.addResult(`Final token valid: ${CsrfValidator.isTokenValid()}`);
      this.addResult(`Final token format valid: ${CsrfValidator.isTokenFormatValid()}`);

      const tokenInfo = CsrfValidator.getTokenInfo();
      this.addResult(`Token info: ${JSON.stringify(tokenInfo)}`);
    } catch (error) {
      this.addResult(`Test error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      this.isLoading = false;
    }
  }

  addResult(message: string) {
    this.testResults = [...this.testResults, `${new Date().toLocaleTimeString()}: ${message}`];
  }

  clearResults() {
    this.testResults = [];
    this.showRateLimitNotification = false;
    this.rateLimitedEndpoint = '';
  }

  async testRateLimiting() {
    this.isLoading = true;
    this.addResult('--- Manual Rate Limiting Test ---');

    try {
      // Make 15 rapid requests to trigger rate limiting
      const requests = [];
      for (let i = 0; i < 15; i++) {
        requests.push(
          ApiWrapper('/csrf-token', {
            method: 'GET',
            includeCsrf: false,
            retryOnRateLimit: false, // Don't retry to see rate limiting in action
          }),
        );
      }

      const results = await Promise.all(requests);
      let rateLimitedCount = 0;

      results.forEach((result, index) => {
        if (result.rateLimitInfo?.isRateLimited) {
          rateLimitedCount++;
          if (rateLimitedCount === 1) {
            // Show notification for first rate limited request
            this.showRateLimitNotification = true;
            this.rateLimitedEndpoint = '/csrf-token';
          }
        }

        this.addResult(
          `Request ${index + 1}: ${result.success ? 'SUCCESS' : 'FAILED'} - ${result.message}`,
        );
      });

      this.addResult(`Total rate limited requests: ${rateLimitedCount}`);
    } catch (error) {
      this.addResult(
        `Rate limiting test error: ${error instanceof Error ? error.message : String(error)}`,
      );
    } finally {
      this.isLoading = false;
    }
  }

  clearRateLimits() {
    RateLimitHandler.clearAllRateLimits();
    this.showRateLimitNotification = false;
    this.rateLimitedEndpoint = '';
    this.addResult('All rate limits cleared');
  }

  render() {
    return (
      <div class="csrf-test">
        <h2>CSRF Implementation Test</h2>

        <div class="controls">
          <button onClick={() => this.runCsrfTests()} disabled={this.isLoading}>
            {this.isLoading ? 'Running Tests...' : 'Run CSRF Tests'}
          </button>

          <button onClick={() => this.testRateLimiting()} disabled={this.isLoading}>
            {this.isLoading ? 'Testing...' : 'Test Rate Limiting'}
          </button>

          <button onClick={() => this.clearRateLimits()} disabled={this.isLoading}>
            Clear Rate Limits
          </button>

          <button onClick={() => this.clearResults()} disabled={this.isLoading}>
            Clear Results
          </button>
        </div>

        <div class="current-state">
          <h3>Current State</h3>
          <p>
            <strong>CSRF Token:</strong>{' '}
            {Store.csrfToken ? Store.csrfToken.substring(0, 20) + '...' : 'none'}
          </p>
          <p>
            <strong>Token Valid:</strong> {CsrfValidator.isTokenValid() ? 'Yes' : 'No'}
          </p>
          <p>
            <strong>Session Active:</strong> {Store.isSessionActive ? 'Yes' : 'No'}
          </p>
        </div>

        <div class="test-results">
          <h3>Test Results</h3>
          <div class="results-list">
            {this.testResults.map((result, index) => (
              <div key={index} class="result-item">
                {result}
              </div>
            ))}
          </div>
        </div>

        {/* Rate Limit Notification */}
        <p-rate-limit-notification
          endpoint={this.rateLimitedEndpoint}
          show={this.showRateLimitNotification}
          autoHide={true}
          hideDelay={10000}
        />
      </div>
    );
  }
}
