import { Var } from '../../var';
import { ApiWrapper } from './ApiWrapper';
import { Store } from '../../store';

export const LogoutApi = async () => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.auth.logout, {
      method: 'POST',
    });

    // Clear CSRF token on successful logout since session is destroyed
    if (result.success) {
      Store.csrfToken = '';
      console.debug('CSRF token cleared after logout');
    }

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error during logout request:', error);
    return {
      success: false,
      message: 'Error during logout request',
      payload: null,
    };
  }
};
