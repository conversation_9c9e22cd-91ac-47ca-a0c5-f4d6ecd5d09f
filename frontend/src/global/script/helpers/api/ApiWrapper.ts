import { Store } from '../../store';
import { ConstructApiUrl } from './ConstructApiUrl';
import { CsrfValidator } from '../security/CsrfValidator';
import { RateLimitHandler } from './RateLimitHandler';

/**
 * Extract rate limiting information from response headers
 */
function extractRateLimitInfo(response: Response) {
  const limit = response.headers.get('RateLimit-Limit');
  const remaining = response.headers.get('RateLimit-Remaining');
  const reset = response.headers.get('RateLimit-Reset');
  const retryAfter = response.headers.get('Retry-After');

  return {
    limit: limit ? parseInt(limit, 10) : undefined,
    remaining: remaining ? parseInt(remaining, 10) : undefined,
    resetTime: reset ? parseInt(reset, 10) : undefined,
    retryAfter: retryAfter ? parseInt(retryAfter, 10) : undefined,
  };
}

/**
 * Sleep for a specified number of milliseconds
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload?: any;
  rateLimitInfo?: {
    isRateLimited: boolean;
    retryAfter?: number;
    limit?: number;
    remaining?: number;
    resetTime?: number;
  };
}

interface ApiOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  credentials?: RequestCredentials;
  includeCsrf?: boolean;
  retryOnRateLimit?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Centralized API wrapper that automatically handles CSRF token updates,
 * rate limiting, and provides consistent error handling across all API calls
 */
export const ApiWrapper = async (
  endpoint: string,
  options: ApiOptions = { method: 'GET' },
): Promise<ApiResponse> => {
  const maxRetries = options.maxRetries ?? 2;
  const retryDelay = options.retryDelay ?? 1000;
  const shouldRetryOnRateLimit = options.retryOnRateLimit ?? false;

  // Check if endpoint is already rate limited
  if (RateLimitHandler.isEndpointRateLimited(endpoint)) {
    const timeRemaining = RateLimitHandler.getTimeUntilReset(endpoint);
    const message = RateLimitHandler.getRateLimitMessage(endpoint);

    console.warn(`Endpoint ${endpoint} is rate limited for ${timeRemaining} more seconds`);

    return {
      success: false,
      message,
      payload: null,
      rateLimitInfo: {
        isRateLimited: true,
        retryAfter: timeRemaining,
      },
    };
  }

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    const result = await performApiRequest(endpoint, options);

    // Record rate limit information
    if (result.rateLimitInfo) {
      if (result.rateLimitInfo.isRateLimited) {
        RateLimitHandler.recordRateLimit(endpoint, result.rateLimitInfo);
      } else if (RateLimitHandler.shouldWarnAboutRateLimit(result.rateLimitInfo)) {
        console.warn(
          'Approaching rate limit:',
          RateLimitHandler.getRateLimitWarning(result.rateLimitInfo),
        );
      }
    }

    // If rate limited and retries are enabled, wait and retry
    if (result.rateLimitInfo?.isRateLimited && shouldRetryOnRateLimit && attempt < maxRetries) {
      const waitTime = result.rateLimitInfo.retryAfter
        ? result.rateLimitInfo.retryAfter * 1000
        : RateLimitHandler.calculateRetryDelay(attempt, retryDelay);

      console.warn(
        `Rate limited, retrying in ${waitTime}ms (attempt ${attempt + 1}/${maxRetries + 1})`,
      );
      await sleep(waitTime);
      continue;
    }

    return result;
  }

  // This should never be reached, but TypeScript requires it
  throw new Error('Unexpected error in ApiWrapper retry logic');
};

/**
 * Perform a single API request without retry logic
 */
async function performApiRequest(endpoint: string, options: ApiOptions): Promise<ApiResponse> {
  const url = ConstructApiUrl(endpoint);

  // Default headers
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add CSRF token for state-changing requests or when explicitly requested
  if (options.includeCsrf !== false && (options.method !== 'GET' || options.includeCsrf === true)) {
    if (!CsrfValidator.isTokenValid()) {
      console.warn(
        'Warning: CSRF token is invalid when creating request headers',
        CsrfValidator.getTokenInfo(),
      );
    }
    defaultHeaders['X-CSRF-Token'] = Store.csrfToken || '';
  }

  // Merge headers
  const headers = { ...defaultHeaders, ...options.headers };

  // Prepare fetch options
  const fetchOptions: RequestInit = {
    method: options.method,
    headers,
    credentials: options.credentials || 'include',
  };

  // Add body for non-GET requests
  if (options.body && options.method !== 'GET') {
    fetchOptions.body =
      typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
  }

  try {
    const response = await fetch(url, fetchOptions);

    // Always check for updated CSRF token in response headers
    const csrfToken = response.headers.get('X-CSRF-Token');
    if (csrfToken) {
      Store.csrfToken = csrfToken;
      console.debug('CSRF token updated from response header:', csrfToken.substring(0, 10) + '...');
    }

    // Extract rate limiting information from headers
    const rateLimitInfo = extractRateLimitInfo(response);

    // Handle non-JSON responses
    let data: any;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = {
        success: response.ok,
        message: response.ok ? 'Request successful' : 'Request failed',
        payload: null,
      };
    }

    // Handle HTTP error status codes
    if (!response.ok) {
      console.error('API request failed:', {
        url,
        status: response.status,
        statusText: response.statusText,
        data,
      });

      // Handle rate limiting (429 status)
      if (response.status === 429) {
        console.warn('Rate limit exceeded:', {
          url,
          rateLimitInfo,
          message: data?.message,
        });

        // Check for CSRF token in rate limit response
        if (data?.csrfToken) {
          Store.csrfToken = data.csrfToken;
          console.debug('CSRF token updated from rate limit response');
        }

        return {
          success: false,
          message: data?.message || 'Rate limit exceeded. Please try again later.',
          payload: data?.payload || null,
          rateLimitInfo: {
            isRateLimited: true,
            ...rateLimitInfo,
          },
        };
      }

      // Handle CSRF token errors specifically
      if (response.status === 403 && data?.code === 'CSRF_TOKEN_INVALID') {
        console.warn('CSRF token invalid, clearing stored token');
        CsrfValidator.clearToken();
      }

      return {
        success: false,
        message: data?.message || `HTTP ${response.status}: ${response.statusText}`,
        payload: data?.payload || null,
        rateLimitInfo: rateLimitInfo.limit ? { isRateLimited: false, ...rateLimitInfo } : undefined,
      };
    }

    return {
      success: data?.success !== false,
      message: data?.message || 'Request successful',
      payload: data?.payload || data,
      rateLimitInfo: rateLimitInfo.limit ? { isRateLimited: false, ...rateLimitInfo } : undefined,
    };
  } catch (error) {
    console.error('API request error:', {
      url,
      error: error instanceof Error ? error.message : String(error),
    });

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Network error occurred',
      payload: null,
    };
  }
}

/**
 * Helper function to get headers with CSRF token
 * @deprecated Use ApiWrapper instead for automatic token management
 */
export const GetHeadersWithCsrf = (): Record<string, string> => {
  if (!Store.csrfToken) {
    console.warn('Warning: CSRF token is empty when creating request headers');
  }

  return {
    'Content-Type': 'application/json',
    'X-CSRF-Token': Store.csrfToken || '',
  };
};
