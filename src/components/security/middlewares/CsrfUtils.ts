import { doubleCsrf } from 'csrf-csrf';
import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';

import { Var } from '../../../global/var';
import { logger } from '../../../global/services';

// SECURITY FIX: Enhanced CSRF configuration with stronger security
const csrfInstance = doubleCsrf({
  getSecret: () => {
    // Use HMAC-based secret derivation for additional security
    const baseSecret = Var.node.express.session.secret;
    return crypto.createHmac('sha256', baseSecret).update('csrf-secret-v2').digest('hex');
  },
  getSessionIdentifier: req => {
    // More secure session identifier with validation
    if (!req.session?.id) {
      throw new Error('No valid session for CSRF protection');
    }
    // Hash session ID for additional security
    return crypto.createHash('sha256').update(req.session.id).digest('hex').substring(0, 16);
  },
  cookieName: Var.node.env === 'prod' ? '__Host-psifi.x-csrf-token' : 'psifi.x-csrf-token',
  cookieOptions: {
    httpOnly: true,
    sameSite: 'lax', // Cross-subdomain compatibility
    secure: Var.node.env === 'prod',
    maxAge: 14 * 24 * 60 * 60 * 1000, // 2 weeks
    path: '/',
  },
  size: 128, // Increased token size for better entropy
  ignoredMethods: ['GET', 'HEAD', 'OPTIONS'],
  getCsrfTokenFromRequest: req => {
    // Check both X-CSRF-Token (preferred) and x-csrf-token (legacy) headers
    const headerToken = (req.headers['x-csrf-token'] || req.headers['X-CSRF-Token']) as string;
    const bodyToken = req.body?._csrf;
    const token = headerToken || bodyToken;

    logger.debug('CSRF token from request:', {
      hasHeaderToken: !!headerToken,
      hasBodyToken: !!bodyToken,
      headerTokenPrefix: headerToken ? headerToken.substring(0, 10) + '...' : 'none',
      path: req.path,
    });

    return token;
  },
});

export const GenerateCsrfToken = csrfInstance.generateCsrfToken;

export function DoubleCsrfProtection(req: Request, res: Response, next: NextFunction) {
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Enhanced logging for all CSRF-protected requests
  const receivedToken = (req.headers['x-csrf-token'] || req.headers['X-CSRF-Token'] || req.body?._csrf) as string;
  const cookieName = Var.node.env === 'prod' ? '__Host-psifi.x-csrf-token' : 'psifi.x-csrf-token';
  const cookieSecret = req.cookies[cookieName];

  logger.debug('CSRF validation check:', {
    path: req.path,
    method: req.method,
    sessionId: req.session?.id,
    receivedToken: receivedToken ? receivedToken.substring(0, 10) + '...' : 'none',
    cookieSecret: cookieSecret ? cookieSecret.substring(0, 10) + '...' : 'none',
  });

  try {
    const doubleCsrfProtection = csrfInstance.doubleCsrfProtection;
    doubleCsrfProtection(req, res, (error?: any) => {
      if (error) {
        // Reverted to simpler logging for the warning, as detailed info is now logged above
        logger.warn('CSRF validation failed:', {
          error: error.message,
          path: req.path,
          method: req.method,
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'no-session',
          hasHeaderToken: !!req.headers['x-csrf-token'],
          hasBodyToken: !!req.body?._csrf,
        });

        return res.status(403).json({
          success: false,
          message: 'Invalid security token. Please refresh the page and try again.',
          code: 'CSRF_TOKEN_INVALID',
        });
      }

      logger.debug('CSRF validation successful:', {
        path: req.path,
        method: req.method,
        sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'no-session',
      });

      next();
    });
  } catch (error) {
    logger.error('CSRF protection error:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      path: req.path,
      method: req.method,
      sessionExists: !!req.session,
      sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'no-session',
    });

    return res.status(500).json({
      success: false,
      message: 'Security validation error. Please try again.',
      details: Var.node.env === 'dev' ? (error instanceof Error ? error.message : String(error)) : undefined,
    });
  }
}
