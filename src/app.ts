import express, { Request, Response, NextFunction } from 'express';
import session from 'express-session';
import connectRedis from 'connect-redis';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import crypto from 'crypto';

import { GenerateApiVersionPath } from './global/helpers';
import { Var } from './global/var';
import { redisClient, logger } from './global/services';
import { SecurityHeaders, PreventParamPollution, ApiLimiter, SanitizeData, CsrfProtection, GenerateCsrfToken, AdditionalSecurityHeaders } from './components/security';
import { HandleErrors, AppError } from './global/middlewares/handlers/HandleErrors';
import { registerAllRoutes } from './routes';

const app = express();
const redisStore = connectRedis(session);

// Trust proxy in production
if (Var.node.env === 'prod') {
  app.set('trust proxy', 1);
}

const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = [Var.app.url.dev, Var.app.url.prod];

    if (!origin) {
      if (Var.node.env === 'dev') {
        return callback(null, true);
      } else {
        return callback(new Error('No origin header provided'), false);
      }
    }

    if (allowedOrigins.indexOf(origin) !== -1 || origin.startsWith('http://localhost')) {
      logger.debug(`CORS: Allowing origin: ${origin}`);
      callback(null, true);
    } else {
      logger.warn(`CORS: Blocked origin: ${origin}`);
      callback(new AppError('Not allowed by CORS', 403));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'Cookie', 'Accept'],
  exposedHeaders: ['X-CSRF-Token', 'Set-Cookie'],
  maxAge: 86400,
  preflightContinue: false,
  optionsSuccessStatus: 204,
};

// Redis client setup
const client = redisClient;

// Session configuration for cross-subdomain usage
const sessionMaxAge = Math.min(Number(Var.node.express.session.maxAge!), 14 * 24 * 60 * 60 * 1000); // Max 14 days

// ===== MIDDLEWARE SETUP =====
// 1. Security and parsing middleware (order matters!)
app.use(SecurityHeaders);
app.use(AdditionalSecurityHeaders); // HARDENING: Additional security headers
app.use(PreventParamPollution);
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));
app.use(cookieParser(Var.node.express.session.secret));
app.use(SanitizeData);

// 2. CORS middleware
app.use(cors(corsOptions));

// 3. Session middleware (requires cookie parser) - Cross-subdomain configuration
app.use(
  session({
    secret: Var.node.express.session.secret!,
    name: Var.node.express.session.name,
    cookie: {
      maxAge: sessionMaxAge,
      secure: Var.node.env === 'prod',
      sameSite: 'lax', // Cross-subdomain support: api.sensefolks.com ↔ app.sensefolks.com
      httpOnly: true,
      path: '/',
      domain: Var.node.env === 'prod' ? Var.app.domain : undefined,
    },
    resave: false,
    saveUninitialized: false,
    store: new redisStore({
      client,
      prefix: 'sess:',
      ttl: Math.floor(sessionMaxAge / 1000),
    }),
    rolling: true,
    unset: 'destroy',
    // Enhanced session ID generation
    genid: () => {
      return crypto.randomBytes(32).toString('hex');
    },
  }),
);
logger.info('Session middleware configured');

// 4. CSRF protection (requires session)
app.use(CsrfProtection);
logger.info('CSRF protection middleware configured');

// 5. Request logging middleware
app.use((req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  res.on('finish', () => {
    const responseTime = Date.now() - start;
    res.locals.responseTime = responseTime;
    logger.request(req, res);
  });
  next();
});

// 6. Rate limiting middleware
app.use(ApiLimiter);

// ===== ROUTES =====
// Health check endpoint
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    service: 'survey-request-api',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  });
});

// CSRF token endpoint
app.get(`${GenerateApiVersionPath()}csrf-token`, (req: Request, res: Response) => {
  try {
    // Ensure session is initialized and saved by setting a property
    if (!req.session.createdAt) {
      req.session.createdAt = Date.now();
    }

    const token = GenerateCsrfToken(req, res);
    res.setHeader('X-CSRF-Token', token);

    const csrfCookieName = Var.node.env === 'prod' ? '__Host-psifi.x-csrf-token' : 'psifi.x-csrf-token';

    logger.debug('CSRF token generated:', {
      sessionExists: !!req.session,
      sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'none',
      cookies: Object.keys(req.cookies || {}),
      hasCsrfCookie: !!req.cookies[csrfCookieName],
      tokenPrefix: token ? token.substring(0, 10) + '...' : 'none',
      cookieTokenPrefix: req.cookies[csrfCookieName] ? req.cookies[csrfCookieName].substring(0, 10) + '...' : 'none',
      match: token === req.cookies[csrfCookieName],
      headers: {
        origin: req.headers.origin,
        referer: req.headers.referer,
        userAgent: req.headers['user-agent'],
      },
    });

    return res.status(200).json({
      success: true,
      message: 'CSRF token generated successfully. Check the X-CSRF-Token header.',
    });
  } catch (error) {
    logger.error('Error generating CSRF token in endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate CSRF token. Please try again.',
    });
  }
});

// CSRF test endpoint
app.post(`${GenerateApiVersionPath()}csrf-test`, (req: Request, res: Response) => {
  const headerToken = (req.headers['x-csrf-token'] || req.headers['X-CSRF-Token']) as string;
  const csrfCookieName = Var.node.env === 'prod' ? '__Host-psifi.x-csrf-token' : 'psifi.x-csrf-token';
  const cookieToken = req.cookies[csrfCookieName];

  logger.info('CSRF test endpoint called:', {
    hasHeaderToken: !!headerToken,
    hasCookieToken: !!cookieToken,
    headerTokenPrefix: headerToken ? headerToken.substring(0, 10) + '...' : 'none',
    cookieTokenPrefix: cookieToken ? cookieToken.substring(0, 10) + '...' : 'none',
    sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'none',
    cookies: Object.keys(req.cookies || {}),
    csrfCookieName,
  });

  return res.status(200).json({
    success: true,
    message: 'CSRF token validation successful',
  });
});

// Application routes
registerAllRoutes(app);

// ===== ERROR HANDLING =====
// 404 handler for undefined routes
app.all('*', (req: Request, _res: Response, next: NextFunction) => {
  next(new AppError(`Cannot find ${req.method} ${req.originalUrl} on this server!`, 404));
});

// Global error handler
app.use(HandleErrors);

process.on('unhandledRejection', (err: Error) => {
  logger.error('UNHANDLED REJECTION! 💥 Shutting down...', err);
  logger.error(`${err.name}: ${err.message}`);
  process.exit(1);
});

process.on('uncaughtException', (err: Error) => {
  logger.error('UNCAUGHT EXCEPTION! 💥 Shutting down...', err);
  logger.error(`${err.name}: ${err.message}`);
  process.exit(1);
});

export default app;
