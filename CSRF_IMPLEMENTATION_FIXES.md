# CSRF Implementation Fixes and Improvements

## Summary of Changes

This document outlines the comprehensive fixes and improvements made to the CSRF implementation across both frontend and backend components.

## Issues Fixed

### 1. Inconsistent Token Refresh
**Problem**: Not all API calls were updating CSRF tokens from response headers.
**Solution**: Created a centralized `ApiWrapper` that automatically handles token updates for all API calls.

### 2. Header Name Mismatch
**Problem**: Backend sent `X-CSRF-Token` but frontend checked for `csrf` or `x-csrf-token`.
**Solution**: Standardized on `X-CSRF-Token` and updated backend to accept both formats for backward compatibility.

### 3. Session Check Inconsistency
**Problem**: `CheckSessionStatusApi` didn't include CSRF headers.
**Solution**: Updated to include CSRF token for consistency with other authenticated requests.

### 4. Logout Token Handling
**Problem**: Logout destroyed session but frontend still tried to update CSRF token.
**Solution**: 
- Backend now clears CSRF cookie on logout
- Frontend clears token and fetches new anonymous token after logout

## New Components Created

### 1. ApiWrapper (`frontend/src/global/script/helpers/api/ApiWrapper.ts`)
- Centralized API request handler
- Automatic CSRF token management
- Consistent error handling
- Automatic token refresh from response headers
- Support for both authenticated and anonymous requests

### 2. CsrfValidator (`frontend/src/global/script/helpers/security/CsrfValidator.ts`)
- Token validation utilities
- Token format checking
- Debug information helpers
- Token refresh management

## Backend Changes

### 1. Enhanced CSRF Middleware (`src/components/security/middlewares/CsrfUtils.ts`)
- Support for both `X-CSRF-Token` and `x-csrf-token` headers
- Improved logging with token prefixes for security
- Better error handling

### 2. Logout Improvements (`src/components/account/helpers/auth/logout.ts`)
- Clear both session and CSRF cookies on logout
- Proper cookie clearing with correct options

### 3. CSRF Test Endpoint (`src/app.ts`)
- Updated to check both header formats
- Better debugging information

## Frontend Changes

### 1. Updated All API Files
The following API files were updated to use the new `ApiWrapper`:
- `GetCsrfTokenApi.ts`
- `CheckSessionStatusApi.ts`
- `loginApi.ts`
- `signupApi.ts`
- `LogoutApi.ts`
- `UpdateByAttributeApi.ts`
- `MailApi.ts`
- `AccountDetailsBySessionApi.ts`
- `GetSurveyApi.ts`
- `createSurveyApi.ts`
- `getAllSurveysApi.ts`
- `getSurveyEmbedCodeApi.ts`
- `passwordResetApi.ts`

### 2. App Initialization Improvements (`frontend/src/components/app-root/app-root.tsx`)
- Sequential CSRF token fetching before session check
- Better error handling during initialization
- Improved logout flow with proper token management

### 3. Helper Exports (`frontend/src/global/script/helpers/index.ts`)
- Added exports for `ApiWrapper` and `CsrfValidator`

## Security Improvements

### 1. Token Validation
- Added format validation for CSRF tokens
- Better token lifecycle management
- Automatic token clearing on errors

### 2. Error Handling
- Specific handling for CSRF validation errors (403 with CSRF_TOKEN_INVALID)
- Automatic token refresh on validation failures
- Better logging for debugging

### 3. Cookie Security
- Proper cookie clearing on logout
- Consistent cookie options across environments
- Support for `__Host-` prefix in production

## Usage Examples

### Using the New ApiWrapper
```typescript
// Simple GET request
const result = await ApiWrapper('/api/endpoint', { method: 'GET' });

// POST request with automatic CSRF handling
const result = await ApiWrapper('/api/endpoint', {
  method: 'POST',
  body: { data: 'value' }
});

// GET request that explicitly includes CSRF token
const result = await ApiWrapper('/api/endpoint', {
  method: 'GET',
  includeCsrf: true
});
```

### Using CsrfValidator
```typescript
// Check if token is valid
if (CsrfValidator.isTokenValid()) {
  // Proceed with request
}

// Ensure valid token before critical operation
await CsrfValidator.ensureValidToken();

// Get debug information
console.log(CsrfValidator.getTokenInfo());
```

## Migration Notes

### For Developers
1. **Use ApiWrapper**: Replace direct `fetch` calls with `ApiWrapper` for automatic CSRF handling
2. **Token Management**: Use `CsrfValidator` utilities instead of direct token manipulation
3. **Error Handling**: The new wrapper provides consistent error responses

### Backward Compatibility
- Old `GetHeadersWithCsrf` function is still available but marked as deprecated
- Backend accepts both old and new header formats
- Existing API calls continue to work during migration

## Testing Recommendations

1. **CSRF Token Flow**: Test token generation, validation, and rotation
2. **Authentication Flow**: Test login, logout, and session management
3. **Error Scenarios**: Test invalid token handling and recovery
4. **Cross-Browser**: Test cookie handling across different browsers
5. **Network Issues**: Test behavior with network failures

## Security Considerations

1. **Token Rotation**: Tokens are rotated on authentication events
2. **Secure Cookies**: Production uses `__Host-` prefix and secure flags
3. **Error Information**: Debug information is limited in production
4. **Token Validation**: Multiple layers of token validation
5. **Session Security**: Proper session regeneration on login

## Performance Impact

- **Minimal Overhead**: Centralized wrapper adds minimal processing time
- **Reduced Requests**: Better token management reduces unnecessary token fetches
- **Caching**: Token validation uses in-memory checks
- **Error Recovery**: Automatic retry logic for token refresh

## Monitoring and Debugging

- Enhanced logging for CSRF operations
- Token prefix logging for security (no full tokens in logs)
- Debug utilities for development
- Error tracking for CSRF validation failures
