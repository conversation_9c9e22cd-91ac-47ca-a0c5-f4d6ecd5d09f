# Rate Limiting Implementation

## Overview

This document outlines the comprehensive rate limiting implementation that ensures the frontend properly handles backend rate limiting responses and provides a smooth user experience.

## Backend Rate Limiting (Analysis)

### Rate Limiting Types
1. **ApiLimiter**: General API rate limiting (10 requests/minute, with specific limits)
2. **AuthLimiter**: Authentication attempts (5 requests/minute)
3. **AccountCreationLimiter**: Account creation (2 requests/minute)
4. **PasswordResetLimiter**: Password reset attempts (3 requests/hour)
5. **EmailVerificationLimiter**: Email verification (3 requests/hour)
6. **SensitiveOperationLimiter**: Sensitive operations (3 requests/minute)
7. **ProgressiveDelayForFailedLogin**: Progressive delays for failed logins

### Rate Limiting Response Format
- **Status Code**: 429 (Too Many Requests)
- **Response Body**: `{ success: false, status: 'error', message: '...', csrfToken?: '...' }`
- **Headers**: Standard rate limiting headers (`RateLimit-Limit`, `RateLimit-Remaining`, etc.)

### Key Backend Features
- CSRF tokens provided in rate limit responses for authenticated users
- Different limits for different endpoints
- Redis-based storage for distributed rate limiting
- IP + email-based key generation for better tracking

## Frontend Rate Limiting Implementation

### 1. Enhanced ApiWrapper

#### Rate Limit Information Extraction
```typescript
interface ApiResponse {
  success: boolean;
  message: string;
  payload?: any;
  rateLimitInfo?: {
    isRateLimited: boolean;
    retryAfter?: number;
    limit?: number;
    remaining?: number;
    resetTime?: number;
  };
}
```

#### Automatic Rate Limit Handling
- Extracts rate limiting information from response headers
- Handles 429 status codes specifically
- Updates CSRF tokens from rate limit responses
- Provides detailed rate limit information in responses

#### Retry Logic
```typescript
interface ApiOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  credentials?: RequestCredentials;
  includeCsrf?: boolean;
  retryOnRateLimit?: boolean;  // New: Enable automatic retries
  maxRetries?: number;         // New: Maximum retry attempts
  retryDelay?: number;         // New: Base retry delay
}
```

### 2. RateLimitHandler Utility

#### Core Features
- **Endpoint Tracking**: Tracks rate limits per endpoint
- **Time Management**: Calculates time until rate limit reset
- **User-Friendly Messages**: Provides contextual error messages
- **Warning System**: Warns when approaching rate limits
- **Automatic Cleanup**: Removes expired rate limits

#### Key Methods
```typescript
// Check if endpoint is rate limited
RateLimitHandler.isEndpointRateLimited(endpoint: string): boolean

// Record a rate limit
RateLimitHandler.recordRateLimit(endpoint: string, rateLimitInfo: RateLimitInfo): void

// Get time until reset
RateLimitHandler.getTimeUntilReset(endpoint: string): number

// Get user-friendly message
RateLimitHandler.getRateLimitMessage(endpoint: string): string

// Clear rate limits
RateLimitHandler.clearAllRateLimits(): void
```

### 3. Rate Limit Notification Component

#### Features
- **Visual Feedback**: Shows rate limit notifications to users
- **Countdown Timer**: Displays time remaining until reset
- **Auto-Hide**: Automatically dismisses after specified time
- **Responsive Design**: Works on mobile and desktop
- **Accessibility**: Proper ARIA labels and keyboard navigation

#### Usage
```typescript
<p-rate-limit-notification
  endpoint="/api/endpoint"
  show={showNotification}
  autoHide={true}
  hideDelay={10000}
/>
```

### 4. Enhanced Error Handling

#### Rate Limit Detection
- Automatically detects 429 status codes
- Extracts rate limit information from headers
- Preserves CSRF tokens from rate limit responses
- Provides detailed error information

#### User Experience Improvements
- **Contextual Messages**: Different messages for different endpoint types
- **Time Formatting**: Human-readable time remaining (e.g., "2m 30s")
- **Progressive Warnings**: Warns when approaching limits (80% usage)
- **Automatic Recovery**: Clears rate limits when they expire

## Implementation Examples

### Basic API Call with Rate Limiting
```typescript
const result = await ApiWrapper('/api/endpoint', {
  method: 'POST',
  body: data,
  retryOnRateLimit: true,
  maxRetries: 3,
});

if (result.rateLimitInfo?.isRateLimited) {
  // Show notification to user
  showRateLimitNotification = true;
}
```

### Manual Rate Limit Checking
```typescript
if (RateLimitHandler.isEndpointRateLimited('/api/endpoint')) {
  const timeRemaining = RateLimitHandler.getTimeUntilReset('/api/endpoint');
  const message = RateLimitHandler.getRateLimitMessage('/api/endpoint');
  
  // Disable button or show warning
  console.warn(`Endpoint rate limited for ${timeRemaining} seconds: ${message}`);
}
```

### Rate Limit Warning
```typescript
if (RateLimitHandler.shouldWarnAboutRateLimit(result.rateLimitInfo)) {
  const warning = RateLimitHandler.getRateLimitWarning(result.rateLimitInfo);
  // Show warning to user
}
```

## Testing

### CSRF Test Component Enhanced
The test component now includes comprehensive rate limiting tests:

1. **Rapid Request Test**: Makes multiple rapid requests to trigger rate limiting
2. **Rate Limit Handler Test**: Tests the rate limit tracking functionality
3. **Notification Test**: Tests the visual notification system
4. **Manual Testing**: Buttons to manually trigger rate limiting scenarios

### Test Scenarios
- Multiple rapid API calls
- Rate limit notification display
- Countdown timer functionality
- Rate limit clearing
- Cross-endpoint rate limit tracking

## Benefits

### For Users
- **Clear Feedback**: Users know when they're rate limited and for how long
- **Automatic Recovery**: System automatically retries when appropriate
- **Contextual Messages**: Different messages for different types of operations
- **Visual Indicators**: Clear notifications with countdown timers

### For Developers
- **Automatic Handling**: Rate limiting handled transparently by ApiWrapper
- **Detailed Information**: Rich rate limit information available
- **Easy Integration**: Simple to add rate limit handling to existing code
- **Debugging Tools**: Comprehensive logging and test utilities

### For System Reliability
- **Graceful Degradation**: System continues to work under rate limiting
- **Reduced Server Load**: Intelligent retry logic prevents unnecessary requests
- **CSRF Preservation**: Maintains security tokens during rate limiting
- **Memory Efficient**: Automatic cleanup of expired rate limits

## Configuration Options

### ApiWrapper Options
- `retryOnRateLimit`: Enable/disable automatic retries (default: false)
- `maxRetries`: Maximum number of retry attempts (default: 2)
- `retryDelay`: Base delay between retries in milliseconds (default: 1000)

### Notification Options
- `autoHide`: Automatically hide notification (default: true)
- `hideDelay`: Time before auto-hiding in milliseconds (default: 5000)

### Rate Limit Handler
- Exponential backoff with jitter for retries
- Configurable warning thresholds (default: 80% of limit)
- Automatic cleanup of expired rate limits

## Security Considerations

1. **CSRF Token Preservation**: Rate limit responses include fresh CSRF tokens
2. **No Sensitive Data Logging**: Only logs safe debugging information
3. **Client-Side Validation**: Validates rate limit information format
4. **Memory Management**: Prevents memory leaks from rate limit tracking

## Future Enhancements

1. **Predictive Rate Limiting**: Warn users before hitting limits
2. **Queue Management**: Queue requests when rate limited
3. **Adaptive Delays**: Adjust retry delays based on server response
4. **Analytics Integration**: Track rate limiting patterns for optimization
5. **Offline Support**: Handle rate limiting when offline/online transitions occur

This implementation provides a robust, user-friendly rate limiting system that enhances both security and user experience while maintaining system reliability.
